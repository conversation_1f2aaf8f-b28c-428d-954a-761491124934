// Educator Feedback Data based on Multiple Intelligence Theory
export const educatorFeedbackData = [
  {
    "Admission Number": "NY24/036",
    "Full Name With Title": "Master. Dasanayalka Mudiyanselage Sayon Thevsas",
    Grade: "Grade 1",
    Category: "Mathematical / Logical Intelligence",
    Description:
      "Demonstrates strong understanding and confidence in Mathematics.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/036",
    "Full Name With Title": "Master. Dasanayalka Mudiyanselage Sayon Thevsas",
    Grade: "Grade 1",
    Category: "Attendance and Punctuality",
    Description: "Shows excellent attendance and punctuality.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/009",
    "Full Name With Title": "Master. Athalage <PERSON>",
    Grade: "Grade 1",
    Category: "Intrapersonal Intelligence",
    Description: "Dress code and personal cleanliness are maintained well.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/009",
    "Full Name With Title": "Master. Athalage <PERSON>",
    Grade: "Grade 1",
    Category: "Interpersonal Intelligence",
    Description:
      "He shows excellent observation and analytical skills for their age. Thinks carefully and notices details in learning activities.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/009",
    "Full Name With Title": "Master. Athalage Don Ayaan <PERSON>mneth",
    Grade: "Grade 1",
    Category: "Music Intelligence",
    Description:
      "Demonstrates remarkable talent and stage presence in dance performances.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/009",
    "Full Name With Title": "Master. Athalage Don Ayaan Nimneth",
    Grade: "Grade 1",
    Category: "Bodily Kinesthetic Intelligence",
    Description: "He is an enrgetic dancer.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/009",
    "Full Name With Title": "Master. Athalage Don Ayaan Nimneth",
    Grade: "Grade 1",
    Category: "Attendance and Punctuality",
    Description: "Consistently present and ready to learn every day.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/014",
    "Full Name With Title": "Master. Ayan Vindeew Hapugala",
    Grade: "Grade 1",
    Category: "Intrapersonal Intelligence",
    Description:
      "He is identified excellent in dress code, individual behaviour, Personal cleanliness, Self awareness, Self reflection, Goal setting, Emotional regulation, Time Management.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/014",
    "Full Name With Title": "Master. Ayan Vindeew Hapugala",
    Grade: "Grade 1",
    Category: "Interpersonal Intelligence",
    Description:
      "A well-disciplined and empathetic child who interacts kindly with others, respects opinions, and works well in teams. Shows great observation and analytical skills. A joy to have in class!",
    Score: 5,
  },
  {
    "Admission Number": "NY24/014",
    "Full Name With Title": "Master. Ayan Vindeew Hapugala",
    Grade: "Grade 1",
    Category: "Music Intelligence",
    Description:
      "He displays great talent and enthusiasm in singing activities.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/014",
    "Full Name With Title": "Master. Ayan Vindeew Hapugala",
    Grade: "Grade 1",
    Category: "Bodily Kinesthetic Intelligence",
    Description:
      "Participates enthusiastically in all PE activities and shows great team spirit.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/014",
    "Full Name With Title": "Master. Ayan Vindeew Hapugala",
    Grade: "Grade 1",
    Category: "Linguistic Intelligence",
    Description:
      "Shows excellent command of spoken English and participates actively in discussions.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/014",
    "Full Name With Title": "Master. Ayan Vindeew Hapugala",
    Grade: "Grade 1",
    Category: "Mathematical / Logical Intelligence",
    Description:
      "Enjoys exploring mathematical concepts and applies them well.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/014",
    "Full Name With Title": "Master. Ayan Vindeew Hapugala",
    Grade: "Grade 1",
    Category: "Attendance and Punctuality",
    Description:
      "Always present and actively participates in all learning activities.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/002",
    "Full Name With Title": "Miss. Gamaathige Vihani Sehansa Wijerathne",
    Grade: "Grade 1",
    Category: "Intrapersonal Intelligence",
    Description:
      "She is identified excellent in dress code, individual behaviour, Personal cleanliness.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/002",
    "Full Name With Title": "Miss. Gamaathige Vihani Sehansa Wijerathne",
    Grade: "Grade 1",
    Category: "Interpersonal Intelligence",
    Description: "Her kindness and empathy shine in the classroom",
    Score: 5,
  },
  {
    "Admission Number": "NY24/002",
    "Full Name With Title": "Miss. Gamaathige Vihani Sehansa Wijerathne",
    Grade: "Grade 1",
    Category: "Music Intelligence",
    Description:
      "She shows great rhythm, expression, and a wonderful talent in singing and dancing activities.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/002",
    "Full Name With Title": "Miss. Gamaathige Vihani Sehansa Wijerathne",
    Grade: "Grade 1",
    Category: "Bodily Kinesthetic Intelligence",
    Description: "A lively and motivated participant in Gymnastic sessions.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/002",
    "Full Name With Title": "Miss. Gamaathige Vihani Sehansa Wijerathne",
    Grade: "Grade 1",
    Category: "Mathematical / Logical Intelligence",
    Description:
      "Enjoys exploring mathematical concepts and applies them well.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/313",
    "Full Name With Title":
      "Miss. Godakumbure Gedara Upeksha Sathsarani Bandara",
    Grade: "Grade 1",
    Category: "Interpersonal Intelligence",
    Description:
      "She interacts well with peers and builds positive relationships.",
    Score: 4,
  },
  {
    "Admission Number": "NY24/313",
    "Full Name With Title":
      "Miss. Godakumbure Gedara Upeksha Sathsarani Bandara",
    Grade: "Grade 1",
    Category: "Music Intelligence",
    Description:
      "She demonstrates good talent in both singing and dance performances.",
    Score: 4,
  },
  {
    "Admission Number": "NY24/313",
    "Full Name With Title":
      "Miss. Godakumbure Gedara Upeksha Sathsarani Bandara",
    Grade: "Grade 1",
    Category: "Bodily Kinesthetic Intelligence",
    Description:
      "Displays energy, effort, and a positive attitude during PE sessions.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/281",
    "Full Name With Title":
      "Master. Dissanayaka Mudiyanselage Sehas Saswindu Bandara",
    Grade: "Grade 1",
    Category: "Interpersonal Intelligence",
    Description:
      "He shows genuine care when someone is feeling down or needs help.",
    Score: 4,
  },
  {
    "Admission Number": "NY24/022",
    "Full Name With Title":
      "Miss. Mudannayake Appuhamilage Dihansa Randuli Mudannayaka",
    Grade: "Grade 1",
    Category: "Attendance and Punctuality",
    Description: "Shows excellent attendance and punctuality.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/365",
    "Full Name With Title": "Master. Gammanchiralage Yumeth Yasodha Wijesiri",
    Grade: "Grade 1",
    Category: "Attendance and Punctuality",
    Description: "Shows good attendance and punctuality.",
    Score: 4,
  },
  {
    "Admission Number": "NY24/020",
    "Full Name With Title":
      "Master. Heenkenda Mudiyanselage Yesandu Yesath Bandara Heenkenda",
    Grade: "Grade 1",
    Category: "Intrapersonal Intelligence",
    Description:
      "He is identified excellent in dress code, individual behaviour, Personal cleanliness.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/020",
    "Full Name With Title":
      "Master. Heenkenda Mudiyanselage Yesandu Yesath Bandara Heenkenda",
    Grade: "Grade 1",
    Category: "Music Intelligence",
    Description:
      "He excels in singing and dancing, bringing creativity and enthusiasm to every performance.",
    Score: 5,
  },
  {
    "Admission Number": "NY24/020",
    "Full Name With Title":
      "Master. Heenkenda Mudiyanselage Yesandu Yesath Bandara Heenkenda",
    Grade: "Grade 1",
    Category: "Mathematical / Logical Intelligence",
    Description:
      "Consistently performs well in Mathematics with keen attention to detail.",
    Score: 4,
  },
  {
    "Admission Number": "NY24/020",
    "Full Name With Title":
      "Master. Heenkenda Mudiyanselage Yesandu Yesath Bandara Heenkenda",
    Grade: "Grade 1",
    Category: "Attendance and Punctuality",
    Description: "Shows good attendance and punctuality.",
    Score: 4,
  },
  {
    "Admission Number": "NY24/005",
    "Full Name With Title":
      "Master. Dahanayaka Ralalage Didula Damsara Rupasingha",
    Grade: "Grade 1",
    Category: "Interpersonal Intelligence",
    Description: "He demonstrates a caring nature through helpful actions.",
    Score: 4,
  },
  {
    "Admission Number": "NY24/005",
    "Full Name With Title":
      "Master. Dahanayaka Ralalage Didula Damsara Rupasingha",
    Grade: "Grade 1",
    Category: "Music Intelligence",
    Description: "A confident and graceful dancer who enjoys performing.",
    Score: 4,
  },
  {
    "Admission Number": "NY24/005",
    "Full Name With Title":
      "Master. Dahanayaka Ralalage Didula Damsara Rupasingha",
    Grade: "Grade 1",
    Category: "Mathematical / Logical Intelligence",
    Description:
      "Enjoys exploring mathematical concepts and applies them well.",
    Score: 4,
  },
  {
    "Admission Number": "NY24/005",
    "Full Name With Title":
      "Master. Dahanayaka Ralalage Didula Damsara Rupasingha",
    Grade: "Grade 1",
    Category: "Attendance and Punctuality",
    Description: "Consistently present and ready to learn every day.",
    Score: 4,
  },
  {
    "Admission Number": "NY24/004",
    "Full Name With Title": "Miss. Kanda Thalappulige Lumini Lisanga",
    Grade: "Grade 1",
    Category: "Intrapersonal Intelligence",
    Description: "She maintains her dress code well.",
    Score: 4,
  },
];

// Helper function to get category ID from category name
export const getCategoryId = (categoryName) => {
  const categoryMap = {
    "Mathematical / Logical Intelligence": "mathematical_logical",
    "Linguistic Intelligence": "linguistic",
    "Interpersonal Intelligence": "interpersonal",
    "Intrapersonal Intelligence": "intrapersonal",
    "Music Intelligence": "music",
    "Bodily Kinesthetic Intelligence": "bodily_kinesthetic",
    "Attendance and Punctuality": "attendance_punctuality",
  };
  return categoryMap[categoryName] || "other";
};

// Helper function to get student calling name from full name
export const getStudentCallingName = (fullName) => {
  const nameMap = {
    "Master. Dasanayalka Mudiyanselage Sayon Thevsas": "Sayon Thevsas",
    "Master. Athalage Don Ayaan Nimneth": "Ayaan Nimneth",
    "Master. Ayan Vindeew Hapugala": "Ayan Vindeew",
    "Miss. Gamaathige Vihani Sehansa Wijerathne": "Vihani Sehansa",
    "Miss. Godakumbure Gedara Upeksha Sathsarani Bandara": "Upeksha Sathsarani",
    "Master. Dissanayaka Mudiyanselage Sehas Saswindu Bandara":
      "Sehas Saswindu",
    "Miss. Mudannayake Appuhamilage Dihansa Randuli Mudannayaka":
      "Dihansa Randuli",
    "Master. Gammanchiralage Yumeth Yasodha Wijesiri": "Yumeth Yasodha",
    "Master. Heenkenda Mudiyanselage Yesandu Yesath Bandara Heenkenda":
      "Yesandu Yesath",
    "Master. Dahanayaka Ralalage Didula Damsara Rupasingha": "Didula Damsara",
    "Miss. Kanda Thalappulige Lumini Lisanga": "Lumini Lisanga",
  };
  return nameMap[fullName] || fullName;
};

// Helper function to group feedback by student
export const groupFeedbackByStudent = () => {
  const grouped = {};

  educatorFeedbackData.forEach((feedback) => {
    const admissionNumber = feedback["Admission Number"];
    if (!grouped[admissionNumber]) {
      grouped[admissionNumber] = {
        admissionNumber,
        fullName: feedback["Full Name With Title"],
        callingName: getStudentCallingName(feedback["Full Name With Title"]),
        grade: feedback["Grade"],
        feedbacks: [],
      };
    }

    grouped[admissionNumber].feedbacks.push({
      category: getCategoryId(feedback["Category"]),
      categoryLabel: feedback["Category"],
      description: feedback["Description"],
      score: feedback["Score"],
    });
  });

  return Object.values(grouped);
};

export default educatorFeedbackData;
