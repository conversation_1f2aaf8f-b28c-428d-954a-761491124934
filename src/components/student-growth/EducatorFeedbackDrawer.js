import React, {
  useState,
  useCallback,
  useMemo,
  useRef,
  useEffect,
} from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Dimensions,
  ActivityIndicator,
} from "react-native";
import BottomSheet, { BottomSheetScrollView } from "@gorhom/bottom-sheet";
import { MaterialIcons } from "@expo/vector-icons";
import { theme } from "../../styles/theme";

// Import the JSON data
import educatorFeedbackData from "../../screens/authenticated/educator/user-actions/drawers/educator_feedback_data.json";

const { width, height } = Dimensions.get("window");

const StarRating = ({ rating, size = 16 }) => {
  const stars = [];
  for (let i = 0; i < 5; i++) {
    stars.push(
      <MaterialIcons
        key={i}
        name={i < rating ? "star" : "star-border"}
        size={size}
        color={i < rating ? "#FFD700" : "#E0E0E0"}
      />
    );
  }
  return <View style={styles.starContainer}>{stars}</View>;
};

// Helper function to get category color
const getCategoryColor = (category) => {
  const categoryColors = {
    "Mathematical / Logical Intelligence": "#4CAF50",
    "Linguistic Intelligence": "#2196F3",
    "Interpersonal Intelligence": "#FF9800",
    "Intrapersonal Intelligence": "#9C27B0",
    "Music Intelligence": "#F44336",
    "Bodily Kinesthetic Intelligence": "#E91E63",
    "Attendance and Punctuality": "#607D8B",
    "Existential Intelligence": "#795548",
  };
  return categoryColors[category] || "#757575";
};

// Helper function to get student calling name
const getStudentCallingName = (fullName) => {
  const nameMap = {
    "Master. Dasanayalka Mudiyanselage Sayon Thevsas": "Sayon Thevsas",
    "Master. Athalage Don Ayaan Nimneth": "Ayaan Nimneth",
    "Master. Ayan Vindeew Hapugala": "Ayan Vindeew",
    "Miss. Gamaathige Vihani Sehansa Wijerathne": "Vihani Sehansa",
    "Miss. Godakumbure Gedara Upeksha Sathsarani Bandara": "Upeksha Sathsarani",
    "Master. Dissanayaka Mudiyanselage Sehas Saswindu Bandara":
      "Sehas Saswindu",
    "Miss. Mudannayake Appuhamilage Dihansa Randuli Mudannayaka":
      "Dihansa Randuli",
    "Master. Gammanchiralage Yumeth Yasodha Wijesiri": "Yumeth Yasodha",
    "Master. Heenkenda Mudiyanselage Yesandu Yesath Bandara Heenkenda":
      "Yesandu Yesath",
    "Master. Dahanayaka Ralalage Didula Damsara Rupasingha": "Didula Damsara",
    "Miss. Kanda Thalappulige Lumini Lisanga": "Lumini Lisanga",
    "Miss. Thanumi Sasithma Dissanayake": "Thanumi Sasithma",
    "Master. Samankula Jothirathnage Thiyon Sayul": "Thiyon Sayul",
    "Miss. Withan Kankanamlage Lagni Akelya Withana": "Lagni Akelya",
    "Master. Liyanage Disas Damyuga Perara": "Disas Damyuga",
    "Master. Zero Senaji": "Zero Senaji",
    "Master. Dhanija Himansa Wanniarachchi": "Dhanija Himansa",
    "Miss. Wilabada Kankanamalage Tharushi Manulya": "Tharushi Manulya",
    "Master. Kenuja Kemgitha Jayon Thilakawardhana": "Kenuja Kemgitha",
    "Master. Kottage Dulina Thinal Dangalle": "Dulina Thinal",
    "Miss. Mudannayaka Appuhamilage Minudi Chenaya Mudannayaka":
      "Minudi Chenaya",
    "Miss. Hewa Komanage Ehansa Harasarani": "Ehansa Harasarani",
    "Miss. Rankothge Vihari Lavanya Rathnayaka": "Vihari Lavanya",
    "Master. Kuruppu Arachchige Yoshika Naveen Jaweera": "Yoshika Naveen",
    "Master. Koongoda Situge Thinula Abinada": "Thinula Abinada",
    "Master. Mawanana Hewage Terash Shehansha Jayasekara": "Terash Shehansha",
    "Master. Loku Gam Hewage Esindu Sharinya": "Esindu Sharinya",
    "Master. Kande Hevayalage Osindu Nethmina Jayarathna": "Osindu Nethmina",
    "Miss. Vinudi Sehansa Dissanayake": "Vinudi Sehansa",
    "Master. Chanuga Abiru Kariyawasam": "Chanuga Abiru",
    "Miss. Rathnayake Mudiyanselage Sayuni Hiyansa Rathnayake":
      "Sayuni Hiyansa",
    "Master. Pathiranalage Maharu Abineth Abeyrathna": "Maharu Abineth",
    "Miss. Kirambaliyana Kankanamge Vidni Tiyasha Jayasinghe": "Vidni Tiyasha",
    "Miss. Dissanayaka Appuhamilage Inuki Aradhya Dissanayaka": "Inuki Aradhya",
    "Master. Mirihagalla Kankanamlage Shiyon Menosha Mirihagalla":
      "Shiyon Menosha",
  };
  return nameMap[fullName] || fullName.split(" ").slice(-2).join(" ");
};

const FeedbackCard = ({ feedback, onReply }) => {
  const [showReplyInput, setShowReplyInput] = useState(false);
  const [replyText, setReplyText] = useState("");

  const handleSendReply = () => {
    if (replyText.trim()) {
      onReply(feedback.id, replyText);
      setReplyText("");
      setShowReplyInput(false);
    }
  };

  return (
    <View style={styles.feedbackCard}>
      {/* Header with student info */}
      <View style={styles.cardHeader}>
        <View style={styles.studentInfo}>
          <View style={styles.studentAvatar}>
            <Text style={styles.avatarText}>
              {getStudentCallingName(feedback["Full Name With Title"])
                .split(" ")
                .map((name) => name[0])
                .join("")
                .toUpperCase()}
            </Text>
          </View>
          <View style={styles.studentDetails}>
            <Text style={styles.studentName}>
              {getStudentCallingName(feedback["Full Name With Title"])}
            </Text>
            <Text style={styles.admissionInfo}>
              {feedback["Admission Number"]}
            </Text>
            <Text style={styles.gradeInfo}>{feedback["Grade"]}</Text>
          </View>
        </View>
      </View>

      {/* Rating and Categories */}
      <View style={styles.ratingSection}>
        <StarRating rating={feedback.Score} size={18} />
        <Text style={styles.ratingText}>{feedback.Score.toFixed(1)}</Text>

        <View style={styles.categoriesContainer}>
          <View
            style={[
              styles.categoryTag,
              styles.primaryCategory,
              { backgroundColor: getCategoryColor(feedback.Category) },
            ]}
          >
            <MaterialIcons name="favorite" size={14} color="#FFFFFF" />
            <Text style={styles.categoryText}>{feedback.Category}</Text>
          </View>
        </View>
      </View>

      {/* Comment Section */}
      <View style={styles.commentSection}>
        <Text style={styles.commentTitle}>Feedback</Text>
        <Text style={styles.commentText}>
          {feedback.Description || "No description provided"}
        </Text>
      </View>

      {/* Reply Section */}
      <View style={styles.replySection}>
        {!showReplyInput ? (
          <TouchableOpacity
            style={styles.replyButton}
            onPress={() => setShowReplyInput(true)}
          >
            <MaterialIcons
              name="reply"
              size={20}
              color={theme.colors.primary}
            />
            <Text style={styles.replyButtonText}>Reply to Educator</Text>
          </TouchableOpacity>
        ) : (
          <View style={styles.replyInputContainer}>
            <TextInput
              style={styles.replyInput}
              placeholder="Parent reply message"
              placeholderTextColor="#999999"
              value={replyText}
              onChangeText={setReplyText}
              multiline
              numberOfLines={3}
            />
            <View style={styles.replyActions}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => {
                  setShowReplyInput(false);
                  setReplyText("");
                }}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.sendButton}
                onPress={handleSendReply}
              >
                <Text style={styles.sendButtonText}>Send</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

const EducatorFeedbackDrawer = ({ isVisible, onClose }) => {
  const bottomSheetRef = useRef(null);
  const [selectedFilter, setSelectedFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const itemsPerPage = 10;

  const snapPoints = useMemo(() => ["25%", "50%", "90%"], []);

  // Process and paginate the data
  const processedData = useMemo(() => {
    // Add unique IDs to each feedback item
    return educatorFeedbackData.map((item, index) => ({
      ...item,
      id: index + 1,
    }));
  }, []);

  // Filter data based on selected filter
  const filteredData = useMemo(() => {
    if (selectedFilter === "all") return processedData;

    return processedData.filter((feedback) => {
      switch (selectedFilter) {
        case "mathematical":
          return feedback.Category.toLowerCase().includes("mathematical");
        case "linguistic":
          return feedback.Category.toLowerCase().includes("linguistic");
        case "interpersonal":
          return feedback.Category.toLowerCase().includes("interpersonal");
        case "intrapersonal":
          return feedback.Category.toLowerCase().includes("intrapersonal");
        case "music":
          return feedback.Category.toLowerCase().includes("music");
        case "kinesthetic":
          return feedback.Category.toLowerCase().includes("kinesthetic");
        case "attendance":
          return feedback.Category.toLowerCase().includes("attendance");
        case "high-rating":
          return feedback.Score >= 4;
        case "needs-attention":
          return feedback.Score < 3;
        default:
          return true;
      }
    });
  }, [processedData, selectedFilter]);

  // Paginate the filtered data
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredData.slice(startIndex, endIndex);
  }, [filteredData, currentPage, itemsPerPage]);

  // Calculate total pages
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);

  const handleSheetChanges = useCallback(
    (index) => {
      if (index === -1) {
        onClose();
      }
    },
    [onClose]
  );

  const handleReply = (feedbackId, replyText) => {
    console.log("Reply to feedback:", feedbackId, replyText);
    // Here you would typically send the reply to your backend
  };

  const handleFilterChange = (filterId) => {
    setSelectedFilter(filterId);
    setCurrentPage(1); // Reset to first page when filter changes
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setLoading(true);
      setTimeout(() => {
        setCurrentPage(currentPage + 1);
        setLoading(false);
      }, 300);
    }
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setLoading(true);
      setTimeout(() => {
        setCurrentPage(currentPage - 1);
        setLoading(false);
      }, 300);
    }
  };

  const filterOptions = [
    { id: "all", label: "All Feedback", icon: "list" },
    { id: "mathematical", label: "Mathematical", icon: "calculate" },
    { id: "linguistic", label: "Linguistic", icon: "language" },
    { id: "interpersonal", label: "Interpersonal", icon: "people" },
    { id: "intrapersonal", label: "Intrapersonal", icon: "psychology" },
    { id: "music", label: "Music", icon: "music-note" },
    { id: "kinesthetic", label: "Kinesthetic", icon: "sports" },
    { id: "attendance", label: "Attendance", icon: "schedule" },
    { id: "high-rating", label: "High Rating (4+)", icon: "star" },
    { id: "needs-attention", label: "Needs Attention (<3)", icon: "warning" },
  ];

  if (!isVisible) return null;

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={1}
      snapPoints={snapPoints}
      onChange={handleSheetChanges}
      enablePanDownToClose
      backgroundStyle={styles.bottomSheetBackground}
      handleIndicatorStyle={styles.handleIndicator}
    >
      <View style={styles.sheetHeader}>
        <Text style={styles.sheetTitle}>Educator Feedback</Text>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <MaterialIcons name="close" size={24} color="#666666" />
        </TouchableOpacity>
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {filterOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.filterTab,
                selectedFilter === option.id && styles.activeFilterTab,
              ]}
              onPress={() => handleFilterChange(option.id)}
            >
              <MaterialIcons
                name={option.icon}
                size={16}
                color={selectedFilter === option.id ? "#FFFFFF" : "#666666"}
              />
              <Text
                style={[
                  styles.filterTabText,
                  selectedFilter === option.id && styles.activeFilterTabText,
                ]}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Pagination Info */}
      <View style={styles.paginationInfo}>
        <Text style={styles.paginationText}>
          Showing {(currentPage - 1) * itemsPerPage + 1}-
          {Math.min(currentPage * itemsPerPage, filteredData.length)} of{" "}
          {filteredData.length} items
        </Text>
        <Text style={styles.pageText}>
          Page {currentPage} of {totalPages}
        </Text>
      </View>

      {/* Feedback List */}
      <BottomSheetScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContentContainer}
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.loadingText}>Loading...</Text>
          </View>
        ) : (
          <>
            {paginatedData.map((feedback) => (
              <FeedbackCard
                key={feedback.id}
                feedback={feedback}
                onReply={handleReply}
              />
            ))}

            {paginatedData.length === 0 && (
              <View style={styles.emptyState}>
                <MaterialIcons name="feedback" size={64} color="#E0E0E0" />
                <Text style={styles.emptyStateText}>
                  No feedback found for "
                  {filterOptions.find((f) => f.id === selectedFilter)?.label}"
                </Text>
              </View>
            )}
          </>
        )}
      </BottomSheetScrollView>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <View style={styles.paginationControls}>
          <TouchableOpacity
            style={[
              styles.paginationButton,
              currentPage === 1 && styles.paginationButtonDisabled,
            ]}
            onPress={handlePrevPage}
            disabled={currentPage === 1 || loading}
          >
            <MaterialIcons
              name="chevron-left"
              size={24}
              color={currentPage === 1 ? "#CCCCCC" : theme.colors.primary}
            />
            <Text
              style={[
                styles.paginationButtonText,
                currentPage === 1 && styles.paginationButtonTextDisabled,
              ]}
            >
              Previous
            </Text>
          </TouchableOpacity>

          <View style={styles.pageIndicator}>
            <Text style={styles.pageIndicatorText}>
              {currentPage} / {totalPages}
            </Text>
          </View>

          <TouchableOpacity
            style={[
              styles.paginationButton,
              currentPage === totalPages && styles.paginationButtonDisabled,
            ]}
            onPress={handleNextPage}
            disabled={currentPage === totalPages || loading}
          >
            <Text
              style={[
                styles.paginationButtonText,
                currentPage === totalPages &&
                  styles.paginationButtonTextDisabled,
              ]}
            >
              Next
            </Text>
            <MaterialIcons
              name="chevron-right"
              size={24}
              color={
                currentPage === totalPages ? "#CCCCCC" : theme.colors.primary
              }
            />
          </TouchableOpacity>
        </View>
      )}
    </BottomSheet>
  );
};

const styles = StyleSheet.create({
  bottomSheetBackground: {
    backgroundColor: "#FFFFFF",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  handleIndicator: {
    backgroundColor: "#E0E0E0",
    width: 40,
  },
  sheetHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#F0F0F0",
  },
  sheetTitle: {
    fontSize: 20,
    fontFamily: theme.fonts.bold,
    color: "#000000",
  },
  closeButton: {
    padding: 4,
  },
  filterContainer: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#F0F0F0",
  },
  filterTab: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: "#F5F5F5",
    gap: 6,
  },
  activeFilterTab: {
    backgroundColor: theme.colors.primary,
  },
  filterTabText: {
    fontSize: 14,
    fontFamily: theme.fonts.medium,
    color: "#666666",
  },
  activeFilterTabText: {
    color: "#FFFFFF",
  },
  paginationInfo: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: "#F8F9FA",
    borderBottomWidth: 1,
    borderBottomColor: "#E0E0E0",
  },
  paginationText: {
    fontSize: 12,
    fontFamily: theme.fonts.regular,
    color: "#666666",
  },
  pageText: {
    fontSize: 12,
    fontFamily: theme.fonts.medium,
    color: theme.colors.primary,
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  scrollContentContainer: {
    paddingBottom: 100, // Add bottom padding to prevent navigation bar overlap
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    fontFamily: theme.fonts.regular,
    color: "#666666",
    marginTop: 12,
  },
  feedbackCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: "#E0E0E0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    marginBottom: 12,
  },
  studentInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  studentAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "#920734",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  avatarText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontFamily: theme.fonts.bold,
  },
  studentDetails: {
    flex: 1,
  },
  studentName: {
    fontSize: 16,
    fontFamily: theme.fonts.bold,
    color: "#000000",
  },
  admissionInfo: {
    fontSize: 12,
    fontFamily: theme.fonts.regular,
    color: "#666666",
  },
  gradeInfo: {
    fontSize: 12,
    fontFamily: theme.fonts.regular,
    color: "#666666",
  },
  ratingSection: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
    flexWrap: "wrap",
  },
  starContainer: {
    flexDirection: "row",
    marginRight: 8,
  },
  ratingText: {
    fontSize: 16,
    fontFamily: theme.fonts.bold,
    color: "#000000",
    marginRight: 12,
  },
  categoriesContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 6,
  },
  categoryTag: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  primaryCategory: {
    backgroundColor: "#920734",
  },
  categoryText: {
    fontSize: 12,
    fontFamily: theme.fonts.medium,
    color: "#FFFFFF",
  },
  commentSection: {
    marginBottom: 16,
  },
  commentTitle: {
    fontSize: 16,
    fontFamily: theme.fonts.bold,
    color: "#000000",
    marginBottom: 8,
  },
  commentText: {
    fontSize: 14,
    fontFamily: theme.fonts.regular,
    color: "#333333",
    lineHeight: 20,
  },
  replySection: {
    borderTopWidth: 1,
    borderTopColor: "#F0F0F0",
    paddingTop: 12,
  },
  replyButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    gap: 8,
  },
  replyButtonText: {
    fontSize: 14,
    fontFamily: theme.fonts.medium,
    color: theme.colors.primary,
  },
  replyInputContainer: {
    gap: 12,
  },
  replyInput: {
    borderWidth: 1,
    borderColor: "#E0E0E0",
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    fontFamily: theme.fonts.regular,
    color: "#000000",
    textAlignVertical: "top",
    minHeight: 80,
  },
  replyActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: 12,
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: "#E0E0E0",
  },
  cancelButtonText: {
    fontSize: 14,
    fontFamily: theme.fonts.medium,
    color: "#666666",
  },
  sendButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: theme.colors.primary,
  },
  sendButtonText: {
    fontSize: 14,
    fontFamily: theme.fonts.medium,
    color: "#FFFFFF",
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    fontFamily: theme.fonts.regular,
    color: "#999999",
    marginTop: 16,
    textAlign: "center",
  },
  paginationControls: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: "#E0E0E0",
    backgroundColor: "#FFFFFF",
  },
  paginationButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: "#F5F5F5",
    gap: 4,
  },
  paginationButtonDisabled: {
    backgroundColor: "#F0F0F0",
  },
  paginationButtonText: {
    fontSize: 14,
    fontFamily: theme.fonts.medium,
    color: theme.colors.primary,
  },
  paginationButtonTextDisabled: {
    color: "#CCCCCC",
  },
  pageIndicator: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
  },
  pageIndicatorText: {
    fontSize: 14,
    fontFamily: theme.fonts.bold,
    color: "#FFFFFF",
  },
});

export default EducatorFeedbackDrawer;
