import React from "react";
import { View, Text, StyleSheet, ScrollView } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function CanteenUserActions() {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>Canteen Actions</Text>
          <Text style={styles.subtitle}>
            Food service tools and management capabilities
          </Text>
        </View>

        <View style={styles.cardContainer}>
          <View style={styles.card}>
            <Text style={styles.cardTitle}>🍽️ Menu Planning</Text>
            <Text style={styles.cardDescription}>
              Create and manage daily meal menus
            </Text>
          </View>

          <View style={styles.card}>
            <Text style={styles.cardTitle}>📋 Order Management</Text>
            <Text style={styles.cardDescription}>
              Process and track student meal orders
            </Text>
          </View>

          <View style={styles.card}>
            <Text style={styles.cardTitle}>📦 Inventory Control</Text>
            <Text style={styles.cardDescription}>
              Manage food supplies and stock levels
            </Text>
          </View>

          <View style={styles.card}>
            <Text style={styles.cardTitle}>🥗 Nutrition Tracking</Text>
            <Text style={styles.cardDescription}>
              Monitor nutritional content and dietary needs
            </Text>
          </View>

          <View style={styles.card}>
            <Text style={styles.cardTitle}>💰 Financial Reports</Text>
            <Text style={styles.cardDescription}>
              Track canteen revenue and expense reports
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    paddingVertical: 30,
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 8,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 16,
    color: "#666666",
    textAlign: "center",
  },
  cardContainer: {
    gap: 16,
  },
  card: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 8,
  },
  cardDescription: {
    fontSize: 14,
    color: "#666666",
    lineHeight: 20,
  },
});