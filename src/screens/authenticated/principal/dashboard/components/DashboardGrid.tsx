import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
} from "react-native-reanimated";
import { DashboardItem } from "../PrincipalDashboardMain";

const { width } = Dimensions.get("window");
const CARD_MARGIN = 10;
const CARDS_PER_ROW = 2;
const CARD_WIDTH = (width - 40 - (CARDS_PER_ROW - 1) * CARD_MARGIN * 2) / CARDS_PER_ROW;

interface DashboardGridProps {
  items: DashboardItem[];
}

const DashboardCard: React.FC<{ item: DashboardItem; index: number }> = ({
  item,
  index,
}) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(0);

  React.useEffect(() => {
    opacity.value = withTiming(1, {
      duration: 500 + index * 100,
    });
  }, []);

  const handlePressIn = () => {
    scale.value = withSpring(0.95);
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  return (
    <Animated.View style={[styles.cardWrapper, animatedStyle]}>
      <TouchableOpacity
        onPress={item.onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.9}
        style={styles.cardTouchable}
      >
        <LinearGradient
          colors={item.gradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.card}
        >
          {/* Icon Container */}
          <View style={styles.iconContainer}>
            <MaterialIcons name={item.icon} size={32} color="white" />
          </View>

          {/* Content */}
          <View style={styles.cardContent}>
            <Text style={styles.cardTitle} numberOfLines={2}>
              {item.title}
            </Text>
            <Text style={styles.cardSubtitle} numberOfLines={1}>
              {item.subtitle}
            </Text>
          </View>

          {/* Arrow Icon */}
          <View style={styles.arrowContainer}>
            <MaterialIcons name="arrow-forward-ios" size={16} color="rgba(255, 255, 255, 0.8)" />
          </View>

          {/* Decorative Elements */}
          <View style={[styles.decorativeCircle, styles.circle1]} />
          <View style={[styles.decorativeCircle, styles.circle2]} />
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );
};

const DashboardGrid: React.FC<DashboardGridProps> = ({ items }) => {
  return (
    <View style={styles.container}>
      {items.map((item, index) => (
        <DashboardCard key={item.id} item={item} index={index} />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  cardWrapper: {
    width: CARD_WIDTH,
    marginBottom: 20,
  },
  cardTouchable: {
    width: "100%",
  },
  card: {
    height: 140,
    borderRadius: 20,
    padding: 20,
    position: "relative",
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 8,
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
  },
  cardContent: {
    flex: 1,
    justifyContent: "space-between",
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "white",
    marginBottom: 4,
    lineHeight: 20,
  },
  cardSubtitle: {
    fontSize: 12,
    color: "rgba(255, 255, 255, 0.9)",
    fontWeight: "500",
  },
  arrowContainer: {
    position: "absolute",
    top: 20,
    right: 20,
  },
  decorativeCircle: {
    position: "absolute",
    borderRadius: 50,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
  },
  circle1: {
    width: 60,
    height: 60,
    top: -20,
    right: -20,
  },
  circle2: {
    width: 40,
    height: 40,
    bottom: -15,
    left: -15,
  },
});

export default DashboardGrid;