import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
} from "react-native-reanimated";
import { DashboardItem } from "../PrincipalDashboardMain";

// Minimal theme colors
const COLORS = {
  darkMaroon: "#920734",
  black: "#000000",
  white: "#FFFFFF",
  lightGray: "#F5F5F5",
  darkGray: "#333333",
  mediumGray: "#666666",
};

const { width } = Dimensions.get("window");
const CARD_MARGIN = 10;
const CARDS_PER_ROW = 2;
const CARD_WIDTH =
  (width - 40 - (CARDS_PER_ROW - 1) * CARD_MARGIN * 2) / CARDS_PER_ROW;

interface DashboardGridProps {
  items: DashboardItem[];
}

const DashboardCard: React.FC<{ item: DashboardItem; index: number }> = ({
  item,
  index,
}) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(0);

  React.useEffect(() => {
    opacity.value = withTiming(1, {
      duration: 500 + index * 100,
    });
  }, []);

  const handlePressIn = () => {
    scale.value = withSpring(0.95);
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  return (
    <Animated.View style={[styles.cardWrapper, animatedStyle]}>
      <TouchableOpacity
        onPress={item.onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.9}
        style={styles.cardTouchable}
      >
        <View
          style={[
            styles.card,
            {
              backgroundColor:
                index % 2 === 0 ? COLORS.darkMaroon : COLORS.black,
            },
          ]}
        >
          {/* Icon Container */}
          <View style={styles.iconContainer}>
            <MaterialIcons name={item.icon} size={28} color={COLORS.white} />
          </View>

          {/* Content */}
          <View style={styles.cardContent}>
            <Text style={styles.cardTitle} numberOfLines={2}>
              {item.title}
            </Text>
            <Text style={styles.cardSubtitle} numberOfLines={1}>
              {item.subtitle}
            </Text>
          </View>

          {/* Arrow Icon */}
          <View style={styles.arrowContainer}>
            <MaterialIcons
              name="arrow-forward-ios"
              size={14}
              color={COLORS.white}
            />
          </View>

          {/* Minimal accent line */}
          <View style={styles.accentLine} />
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const DashboardGrid: React.FC<DashboardGridProps> = ({ items }) => {
  return (
    <View style={styles.container}>
      {items.map((item, index) => (
        <DashboardCard key={item.id} item={item} index={index} />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  cardWrapper: {
    width: CARD_WIDTH,
    marginBottom: 20,
  },
  cardTouchable: {
    width: "100%",
  },
  card: {
    height: 130,
    borderRadius: 12,
    padding: 18,
    position: "relative",
    overflow: "hidden",
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
  },
  iconContainer: {
    width: 44,
    height: 44,
    borderRadius: 8,
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
  },
  cardContent: {
    flex: 1,
    justifyContent: "space-between",
  },
  cardTitle: {
    fontSize: 15,
    fontWeight: "600",
    color: COLORS.white,
    marginBottom: 4,
    lineHeight: 18,
  },
  cardSubtitle: {
    fontSize: 11,
    color: "rgba(255, 255, 255, 0.8)",
    fontWeight: "400",
  },
  arrowContainer: {
    position: "absolute",
    top: 18,
    right: 18,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    justifyContent: "center",
    alignItems: "center",
  },
  accentLine: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 2,
    backgroundColor: COLORS.white,
    opacity: 0.3,
  },
});

export default DashboardGrid;
