import React, { useRef } from "react";
import { View, Text, StyleSheet, ScrollView } from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { Modalize } from "react-native-modalize";
import DashboardGrid from "./components/DashboardGrid";
import StudentsModal from "./modals/StudentsModal";
import TeachersModal from "./modals/TeachersModal";
import SportCoachesModal from "./modals/SportCoachesModal";
import EducatorFeedbackModal from "./modals/EducatorFeedbackModal";
import AnnouncementsModal from "./modals/AnnouncementsModal";
import AcademicReportsModal from "./modals/AcademicReportsModal";
import SchoolFacilitiesModal from "./modals/SchoolFacilitiesModal";
import FinancialOverviewModal from "./modals/FinancialOverviewModal";
import ParentCommunicationModal from "./modals/ParentCommunicationModal";
import EmergencyManagementModal from "./modals/EmergencyManagementModal";

export interface DashboardItem {
  id: string;
  title: string;
  subtitle: string;
  icon: keyof typeof MaterialIcons.glyphMap;
  color: string;
  gradient: [string, string];
  onPress: () => void;
}

export default function PrincipalDashboardMain() {
  // Modal refs
  const studentsModalRef = useRef<Modalize>(null);
  const teachersModalRef = useRef<Modalize>(null);
  const sportCoachesModalRef = useRef<Modalize>(null);
  const educatorFeedbackModalRef = useRef<Modalize>(null);
  const announcementsModalRef = useRef<Modalize>(null);
  const academicReportsModalRef = useRef<Modalize>(null);
  const schoolFacilitiesModalRef = useRef<Modalize>(null);
  const financialOverviewModalRef = useRef<Modalize>(null);
  const parentCommunicationModalRef = useRef<Modalize>(null);
  const emergencyManagementModalRef = useRef<Modalize>(null);

  const dashboardItems: DashboardItem[] = [
    {
      id: "all_students",
      title: "All Students",
      subtitle: "Student Management",
      icon: "school",
      color: "#0057FF",
      gradient: ["#0057FF", "#3d7cff"],
      onPress: () => studentsModalRef.current?.open(),
    },
    {
      id: "all_teachers",
      title: "All Teachers",
      subtitle: "Staff Directory",
      icon: "people",
      color: "#920734",
      gradient: ["#920734", "#b8285a"],
      onPress: () => teachersModalRef.current?.open(),
    },
    {
      id: "sport_coaches",
      title: "Sport Coaches",
      subtitle: "Sports Management",
      icon: "sports-soccer",
      color: "#0057FF",
      gradient: ["#0057FF", "#3d7cff"],
      onPress: () => sportCoachesModalRef.current?.open(),
    },
    {
      id: "educator_feedback",
      title: "Educator Feedback",
      subtitle: "Performance Reviews",
      icon: "rate-review",
      color: "#920734",
      gradient: ["#920734", "#b8285a"],
      onPress: () => educatorFeedbackModalRef.current?.open(),
    },
    {
      id: "announcements",
      title: "Announcements",
      subtitle: "School Communications",
      icon: "campaign",
      color: "#0057FF",
      gradient: ["#0057FF", "#3d7cff"],
      onPress: () => announcementsModalRef.current?.open(),
    },
    // {
    //   id: "academic_reports",
    //   title: "Academic Reports",
    //   subtitle: "Performance Analytics",
    //   icon: "assessment",
    //   color: "#920734",
    //   gradient: ["#920734", "#b8285a"],
    //   onPress: () => academicReportsModalRef.current?.open(),
    // },
    // {
    //   id: "school_facilities",
    //   title: "School Facilities",
    //   subtitle: "Infrastructure Management",
    //   icon: "business",
    //   color: "#0057FF",
    //   gradient: ["#0057FF", "#3d7cff"],
    //   onPress: () => schoolFacilitiesModalRef.current?.open(),
    // },
    // {
    //   id: "financial_overview",
    //   title: "Financial Overview",
    //   subtitle: "Budget & Expenses",
    //   icon: "account-balance",
    //   color: "#920734",
    //   gradient: ["#920734", "#b8285a"],
    //   onPress: () => financialOverviewModalRef.current?.open(),
    // },
    // {
    //   id: "parent_communications",
    //   title: "Parent Communications",
    //   subtitle: "Parent-School Hub",
    //   icon: "family-restroom",
    //   color: "#0057FF",
    //   gradient: ["#0057FF", "#3d7cff"],
    //   onPress: () => parentCommunicationModalRef.current?.open(),
    // },
    // {
    //   id: "emergency_management",
    //   title: "Emergency Management",
    //   subtitle: "Safety & Protocols",
    //   icon: "emergency",
    //   color: "#920734",
    //   gradient: ["#920734", "#b8285a"],
    //   onPress: () => emergencyManagementModalRef.current?.open(),
    // },
  ];

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Dashboard</Text>
      </View>

      {/* Dashboard Grid */}
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        <DashboardGrid items={dashboardItems} />
      </ScrollView>

      {/* Modals */}
      <StudentsModal ref={studentsModalRef} />
      <TeachersModal ref={teachersModalRef} />
      <SportCoachesModal ref={sportCoachesModalRef} />
      <EducatorFeedbackModal ref={educatorFeedbackModalRef} />
      <AnnouncementsModal ref={announcementsModalRef} />
      <AcademicReportsModal ref={academicReportsModalRef} />
      <SchoolFacilitiesModal ref={schoolFacilitiesModalRef} />
      <FinancialOverviewModal ref={financialOverviewModalRef} />
      <ParentCommunicationModal ref={parentCommunicationModalRef} />
      <EmergencyManagementModal ref={emergencyManagementModalRef} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 4,
    paddingBottom: 8,
    backgroundColor: "white",
    borderBottomWidth: 0,
    borderBottomColor: "#e0e0e0",
  },
  headerTitle: {
    fontSize: 24,
    color: "#1a1a1a",
    textAlign: "center",
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
});
