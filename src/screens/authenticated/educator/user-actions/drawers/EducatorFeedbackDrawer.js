import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Image,
  ActivityIndicator,
} from "react-native";
import { useSelector, useDispatch } from "react-redux";
import { MaterialIcons } from "@expo/vector-icons";
import StarRating from "react-native-star-rating-widget";
import { theme } from "../../../../../styles/theme";
import { USER_CATEGORIES } from "../../../../../constants/userCategories";
import {
  fetchEducatorFeedbacks,
  submitEducatorFeedback,
} from "../../../../../state-store/slices/educator/educatorFeedbackSlice";
import { groupFeedbackByStudent } from "../../../../../data/educatorFeedbackData";

// Import fallback profile image
const defaultProfileImage = require("../../../../../assets/images/sample-profile.png");

const EducatorFeedbackDrawer = ({ modalRef }) => {
  const dispatch = useDispatch();
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);

  // Helper function to safely get profile image
  const getProfileImage = (student) => {
    try {
      if (student && student.profile_picture) {
        return { uri: student.profile_picture };
      }
      return defaultProfileImage;
    } catch (error) {
      console.warn("Error loading profile image:", error);
      return defaultProfileImage;
    }
  };
  const [rating, setRating] = useState(0);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [comment, setComment] = useState("");

  // Get global state
  const { sessionData } = useSelector((state) => state.app);
  const { feedbacks, loading, error, submitting } = useSelector(
    (state) => state.educatorFeedback
  );

  // Student data from the provided JSON
  const classStudents = [
    {
      id: 1,
      student_calling_name: "Sayon Thevsas",
      full_name: "Master. Dasanayalka Mudiyanselage Sayon Thevsas",
      admission_number: "NY24/036",
      grade: "Grade 1",
      profile_picture: null,
    },
    {
      id: 2,
      student_calling_name: "Ayaan Nimneth",
      full_name: "Master. Athalage Don Ayaan Nimneth",
      admission_number: "NY24/009",
      grade: "Grade 1",
      profile_picture: null,
    },
    {
      id: 3,
      student_calling_name: "Ayan Vindeew",
      full_name: "Master. Ayan Vindeew Hapugala",
      admission_number: "NY24/014",
      grade: "Grade 1",
      profile_picture: null,
    },
    {
      id: 4,
      student_calling_name: "Vihani Sehansa",
      full_name: "Miss. Gamaathige Vihani Sehansa Wijerathne",
      admission_number: "NY24/002",
      grade: "Grade 1",
      profile_picture: null,
    },
    {
      id: 5,
      student_calling_name: "Upeksha Sathsarani",
      full_name: "Miss. Godakumbure Gedara Upeksha Sathsarani Bandara",
      admission_number: "NY24/313",
      grade: "Grade 1",
      profile_picture: null,
    },
    {
      id: 6,
      student_calling_name: "Sehas Saswindu",
      full_name: "Master. Dissanayaka Mudiyanselage Sehas Saswindu Bandara",
      admission_number: "NY24/281",
      grade: "Grade 1",
      profile_picture: null,
    },
    {
      id: 7,
      student_calling_name: "Dihansa Randuli",
      full_name: "Miss. Mudannayake Appuhamilage Dihansa Randuli Mudannayaka",
      admission_number: "NY24/022",
      grade: "Grade 1",
      profile_picture: null,
    },
    {
      id: 8,
      student_calling_name: "Yumeth Yasodha",
      full_name: "Master. Gammanchiralage Yumeth Yasodha Wijesiri",
      admission_number: "NY24/365",
      grade: "Grade 1",
      profile_picture: null,
    },
    {
      id: 9,
      student_calling_name: "Yesandu Yesath",
      full_name:
        "Master. Heenkenda Mudiyanselage Yesandu Yesath Bandara Heenkenda",
      admission_number: "NY24/020",
      grade: "Grade 1",
      profile_picture: null,
    },
    {
      id: 10,
      student_calling_name: "Didula Damsara",
      full_name: "Master. Dahanayaka Ralalage Didula Damsara Rupasingha",
      admission_number: "NY24/005",
      grade: "Grade 1",
      profile_picture: null,
    },
    {
      id: 11,
      student_calling_name: "Lumini Lisanga",
      full_name: "Miss. Kanda Thalappulige Lumini Lisanga",
      admission_number: "NY24/004",
      grade: "Grade 1",
      profile_picture: null,
    },
  ];

  // Feedback categories based on Multiple Intelligence Theory
  const feedbackCategories = [
    {
      id: "mathematical_logical",
      label: "Mathematical / Logical Intelligence",
      color: "#4CAF50",
    },
    { id: "linguistic", label: "Linguistic Intelligence", color: "#2196F3" },
    {
      id: "interpersonal",
      label: "Interpersonal Intelligence",
      color: "#FF9800",
    },
    {
      id: "intrapersonal",
      label: "Intrapersonal Intelligence",
      color: "#9C27B0",
    },
    { id: "music", label: "Music Intelligence", color: "#F44336" },
    {
      id: "bodily_kinesthetic",
      label: "Bodily Kinesthetic Intelligence",
      color: "#E91E63",
    },
    {
      id: "attendance_punctuality",
      label: "Attendance and Punctuality",
      color: "#607D8B",
    },
  ];

  // Fetch existing feedbacks on component mount
  useEffect(() => {
    dispatch(fetchEducatorFeedbacks({ page: 1, limit: 50 }));
  }, [dispatch]);

  // Generate mock feedbacks from the grouped data
  const generateMockFeedbacks = () => {
    const groupedData = groupFeedbackByStudent();
    return groupedData
      .map((studentData, index) => {
        // Find matching student from classStudents
        const student = classStudents.find(
          (s) => s.admission_number === studentData.admissionNumber
        );

        if (!student) return null;

        // Calculate average score
        const averageScore =
          studentData.feedbacks.reduce((sum, f) => sum + f.score, 0) /
          studentData.feedbacks.length;

        // Get all categories
        const categories = studentData.feedbacks.map((f) => f.category);

        // Combine all descriptions
        const comment = studentData.feedbacks
          .map((f) => f.description)
          .join(". ");

        return {
          id: index + 1,
          student,
          rating: Math.round(averageScore),
          categories,
          comment,
          status: "Approved",
          created_by: "Ms. Sarah Johnson",
          approved_by: "Principal Mr. Asanka",
          created_at: new Date(
            Date.now() - index * 24 * 60 * 60 * 1000
          ).toISOString(),
          revision_instructions: null,
        };
      })
      .filter(Boolean);
  };

  const mockFeedbacks = generateMockFeedbacks();

  // Use Redux feedbacks if available, otherwise use mock data
  const existingFeedbacks = feedbacks.length > 0 ? feedbacks : mockFeedbacks;

  const toggleCategory = (categoryId) => {
    setSelectedCategories((prev) =>
      prev.includes(categoryId)
        ? prev.filter((id) => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const handleSubmitFeedback = async () => {
    if (
      !selectedStudent ||
      rating === 0 ||
      selectedCategories.length === 0 ||
      !comment.trim()
    ) {
      Alert.alert("Error", "Please fill in all required fields");
      return;
    }

    try {
      const feedbackData = {
        student_id: selectedStudent.id,
        student_name: selectedStudent.student_calling_name,
        rating,
        categories: selectedCategories,
        comment: comment.trim(),
        educator_id: sessionData?.data?.id || sessionData?.id,
        educator_name: sessionData?.data?.full_name || sessionData?.full_name,
      };

      await dispatch(submitEducatorFeedback(feedbackData)).unwrap();

      Alert.alert("Success", "Feedback submitted successfully");

      // Reset form
      setSelectedStudent(null);
      setRating(0);
      setSelectedCategories([]);
      setComment("");
      setShowAddForm(false);
    } catch (error) {
      Alert.alert("Error", error.message || "Failed to submit feedback");
    }
  };

  const renderStudentSelector = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Select Student</Text>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.studentScroll}
      >
        {classStudents.map((student) => (
          <TouchableOpacity
            key={student.id}
            style={[
              styles.studentCard,
              selectedStudent?.id === student.id && styles.selectedStudentCard,
            ]}
            onPress={() => setSelectedStudent(student)}
          >
            <View style={styles.studentAvatar}>
              <Image
                source={getProfileImage(student)}
                style={styles.avatarImage}
                onError={() => {
                  console.warn(
                    "Failed to load profile image for student:",
                    student?.id
                  );
                }}
              />
            </View>
            <Text style={styles.studentName}>
              {student.student_calling_name}
            </Text>
            <Text style={styles.studentInfo}>{student.admission_number}</Text>
            <Text style={styles.studentInfo}>{student.grade}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderRatingSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Rating</Text>
      <View style={styles.ratingContainer}>
        <StarRating
          rating={rating}
          onChange={setRating}
          starSize={32}
          color={theme.colors.primary}
          emptyColor={theme.colors.border}
        />
        <Text style={styles.ratingText}>{rating}.0</Text>
      </View>
    </View>
  );

  const renderCategoriesSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Categories</Text>
      <View style={styles.categoriesContainer}>
        {feedbackCategories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryChip,
              selectedCategories.includes(category.id) && {
                backgroundColor: category.color,
              },
            ]}
            onPress={() => toggleCategory(category.id)}
          >
            <Text
              style={[
                styles.categoryText,
                selectedCategories.includes(category.id) &&
                  styles.selectedCategoryText,
              ]}
            >
              {category.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderCommentSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Comment</Text>
      <TextInput
        style={styles.commentInput}
        multiline
        numberOfLines={4}
        placeholder="Enter your feedback comment..."
        value={comment}
        onChangeText={setComment}
        textAlignVertical="top"
      />
    </View>
  );

  const renderAddForm = () => (
    <View style={styles.addFormContainer}>
      <View style={styles.formHeader}>
        <Text style={styles.formTitle}>Add New Feedback</Text>
        <TouchableOpacity onPress={() => setShowAddForm(false)}>
          <MaterialIcons name="close" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.addFormScrollContent}
      >
        {renderStudentSelector()}
        {renderRatingSection()}
        {renderCategoriesSection()}
        {renderCommentSection()}

        <View style={styles.formActions}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => setShowAddForm(false)}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.saveButton}
            onPress={handleSubmitFeedback}
          >
            <Text style={styles.saveButtonText}>Save</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header with Add Button */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <MaterialIcons
            name="rate-review"
            size={24}
            color={theme.colors.primary}
          />
          <Text style={styles.title}>Educator Feedbacks Grade 1</Text>
        </View>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowAddForm(true)}
          disabled={submitting}
        >
          <MaterialIcons name="add" size={20} color="white" />
          <Text style={styles.addButtonText}>Add Feedback</Text>
        </TouchableOpacity>
      </View>

      {/* Loading State */}
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading feedbacks...</Text>
        </View>
      )}

      {/* Error State */}
      {error && (
        <View style={styles.errorContainer}>
          <MaterialIcons name="error-outline" size={48} color="#F44336" />
          <Text style={styles.errorText}>Failed to load feedbacks</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() =>
              dispatch(fetchEducatorFeedbacks({ page: 1, limit: 50 }))
            }
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      )}

      {showAddForm ? (
        renderAddForm()
      ) : (
        <ScrollView
          style={styles.feedbacksList}
          contentContainerStyle={styles.feedbacksListContent}
          showsVerticalScrollIndicator={false}
        >
          {existingFeedbacks.map((feedback) => (
            <View key={feedback.id} style={styles.feedbackCard}>
              <View style={styles.feedbackHeader}>
                <View style={styles.studentInfo}>
                  <View style={styles.studentAvatar}>
                    <Image
                      source={getProfileImage(feedback?.student)}
                      style={styles.avatarImage}
                      onError={() => {
                        console.warn(
                          "Failed to load profile image for feedback:",
                          feedback?.id
                        );
                      }}
                    />
                  </View>
                  <View style={styles.studentDetails}>
                    <Text style={styles.studentName}>
                      {feedback?.student?.student_calling_name ||
                        "Unknown Student"}
                    </Text>
                    <Text style={styles.studentMeta}>
                      {feedback?.student?.admission_number || "N/A"} •{" "}
                      {feedback?.student?.grade || "N/A"}
                    </Text>
                  </View>
                </View>
                <View style={styles.feedbackRating}>
                  <StarRating
                    rating={feedback?.rating || 0}
                    onChange={() => {}} // Read-only
                    starSize={16}
                    color={theme.colors.primary}
                    emptyColor={theme.colors.border}
                    enableHalfStar={false}
                  />
                  <Text style={styles.ratingValue}>
                    {feedback?.rating || 0}.0
                  </Text>
                </View>
              </View>

              <View style={styles.feedbackCategories}>
                {(feedback?.categories || []).map((categoryId) => {
                  const category = feedbackCategories.find(
                    (c) => c.id === categoryId
                  );
                  return category ? (
                    <View
                      key={categoryId}
                      style={[
                        styles.categoryBadge,
                        { backgroundColor: category.color },
                      ]}
                    >
                      <Text style={styles.categoryBadgeText}>
                        {category.label}
                      </Text>
                    </View>
                  ) : null;
                })}
              </View>

              <Text style={styles.feedbackComment}>
                {feedback?.comment || "No comment provided"}
              </Text>

              <View style={styles.feedbackMeta}>
                <Text style={styles.metaText}>
                  By {feedback?.created_by || "Unknown"}
                </Text>
                <Text style={styles.metaText}>
                  {feedback?.created_at
                    ? new Date(feedback.created_at).toLocaleDateString()
                    : "Unknown date"}
                </Text>
              </View>

              <View style={styles.feedbackStatus}>
                <View
                  style={[
                    styles.statusBadge,
                    {
                      backgroundColor:
                        feedback?.status === "Approved" ? "#4CAF50" : "#FF9800",
                    },
                  ]}
                >
                  <Text style={styles.statusText}>
                    {feedback?.status || "Pending"}
                  </Text>
                </View>
                {feedback?.status !== "Approved" &&
                  feedback?.status !== "Cancelled" && (
                    <View style={styles.feedbackActions}>
                      <TouchableOpacity style={styles.actionButton}>
                        <Text style={styles.actionButtonText}>Cancel</Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[styles.actionButton, styles.saveActionButton]}
                      >
                        <Text
                          style={[
                            styles.actionButtonText,
                            styles.saveActionButtonText,
                          ]}
                        >
                          Save
                        </Text>
                      </TouchableOpacity>
                    </View>
                  )}
              </View>

              {feedback?.revision_instructions && (
                <View style={styles.revisionSection}>
                  <Text style={styles.revisionTitle}>
                    Revision Instructions
                  </Text>
                  <Text style={styles.revisionText}>
                    {feedback.revision_instructions}
                  </Text>
                </View>
              )}
            </View>
          ))}
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    color: theme.colors.text,
    marginLeft: theme.spacing.sm,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: theme.spacing.xl,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.secondary,
    marginTop: theme.spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: theme.spacing.xl,
  },
  errorText: {
    fontSize: 16,
    color: "#F44336",
    marginTop: theme.spacing.md,
    textAlign: "center",
  },
  retryButton: {
    marginTop: theme.spacing.lg,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
  },
  retryButtonText: {
    color: "white",
    fontSize: 14,
    fontWeight: "600",
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
  },
  addButtonText: {
    color: "white",
    fontWeight: "600",
    marginLeft: theme.spacing.xs,
  },
  addFormContainer: {
    flex: 1,
    padding: theme.spacing.md,
  },
  addFormScrollContent: {
    paddingBottom: 100, // Add bottom padding to prevent navigation bar overlap
  },
  formHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: theme.spacing.lg,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: theme.colors.text,
  },
  section: {
    marginBottom: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  studentScroll: {
    flexDirection: "row",
  },
  studentCard: {
    backgroundColor: theme.colors.card,
    padding: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    marginRight: theme.spacing.sm,
    alignItems: "center",
    minWidth: 100,
    borderWidth: 2,
    borderColor: "transparent",
  },
  selectedStudentCard: {
    borderColor: theme.colors.primary,
  },
  studentAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.background,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: theme.spacing.xs,
  },
  avatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  studentName: {
    fontSize: 12,
    fontWeight: "600",
    color: theme.colors.text,
    textAlign: "center",
  },
  studentInfo: {
    fontSize: 10,
    color: theme.colors.secondary,
    textAlign: "center",
  },
  studentMeta: {
    fontSize: 12,
    color: theme.colors.secondary,
  },
  studentDetails: {
    flex: 1,
    marginLeft: theme.spacing.sm,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  ratingText: {
    fontSize: 18,
    fontWeight: "bold",
    color: theme.colors.text,
    marginLeft: theme.spacing.md,
  },
  categoriesContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  categoryChip: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
    backgroundColor: theme.colors.background,
    marginRight: theme.spacing.sm,
    marginBottom: theme.spacing.sm,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  categoryText: {
    fontSize: 14,
    color: theme.colors.text,
  },
  selectedCategoryText: {
    color: "white",
    fontWeight: "600",
  },
  commentInput: {
    backgroundColor: theme.colors.card,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    fontSize: 14,
    color: theme.colors.text,
    minHeight: 100,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  formActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: theme.spacing.lg,
    paddingBottom: theme.spacing.xl, // Add bottom padding to move buttons up
  },
  cancelButton: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    marginRight: theme.spacing.sm,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  cancelButtonText: {
    textAlign: "center",
    fontSize: 16,
    fontWeight: "600",
    color: theme.colors.text,
  },
  saveButton: {
    flex: 1,
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    marginLeft: theme.spacing.sm,
  },
  saveButtonText: {
    textAlign: "center",
    fontSize: 16,
    fontWeight: "600",
    color: "white",
  },
  feedbacksList: {
    flex: 1,
    padding: theme.spacing.md,
  },
  feedbacksListContent: {
    paddingBottom: 100, // Add bottom padding to prevent navigation bar overlap
  },
  feedbackCard: {
    backgroundColor: theme.colors.card,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.md,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  feedbackHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: theme.spacing.md,
  },
  feedbackRating: {
    alignItems: "center",
  },
  ratingValue: {
    fontSize: 12,
    color: theme.colors.text,
    marginTop: theme.spacing.xs,
  },
  feedbackCategories: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: theme.spacing.md,
  },
  categoryBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
    marginRight: theme.spacing.xs,
    marginBottom: theme.spacing.xs,
  },
  categoryBadgeText: {
    fontSize: 12,
    color: "white",
    fontWeight: "600",
  },
  feedbackComment: {
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 20,
    marginBottom: theme.spacing.md,
  },
  feedbackMeta: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: theme.spacing.md,
  },
  metaText: {
    fontSize: 12,
    color: theme.colors.secondary,
  },
  feedbackStatus: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
  },
  statusText: {
    fontSize: 12,
    color: "white",
    fontWeight: "600",
  },
  feedbackActions: {
    flexDirection: "row",
  },
  actionButton: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.sm,
    marginLeft: theme.spacing.sm,
    backgroundColor: theme.colors.background,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  saveActionButton: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  actionButtonText: {
    fontSize: 12,
    color: theme.colors.text,
    fontWeight: "600",
  },
  saveActionButtonText: {
    color: "white",
  },
  revisionSection: {
    marginTop: theme.spacing.md,
    padding: theme.spacing.md,
    backgroundColor: theme.colors.background,
    borderRadius: theme.borderRadius.sm,
    borderLeftWidth: 3,
    borderLeftColor: "#FF9800",
  },
  revisionTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  revisionText: {
    fontSize: 12,
    color: theme.colors.secondary,
    lineHeight: 18,
  },
});

export default EducatorFeedbackDrawer;
